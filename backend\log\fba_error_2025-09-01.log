2025-08-29 10:12:21.014 | ERROR    | 86a391aff27c483699fb7e196425abaf | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'assertion': {'source': <AssertionSource.COOKIES: 'cookies'>, 'type': <AssertionType.GREATER_THAN: 'greater_than'>, 'path': 'laboris cillum et minim', 'expected': None, 'message': 'velit pariatur enim in'}, 'success': False, 'actual': None, 'message': 'velit pariatur enim in: 期望大于: None, 实际值: None'})}

2025-08-29 10:12:21.016 | ERROR    | 86a391aff27c483699fb7e196425abaf | Exception in ASGI application

2025-08-29 10:13:25.502 | ERROR    | e99f49dff72f416f8fc2f969341ee5e6 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'assertion': {'source': <AssertionSource.COOKIES: 'cookies'>, 'type': <AssertionType.GREATER_THAN: 'greater_than'>, 'path': 'laboris cillum et minim', 'expected': None, 'message': 'velit pariatur enim in'}, 'success': False, 'actual': None, 'message': 'velit pariatur enim in: 期望大于: None, 实际值: None'})}

2025-08-29 10:13:25.504 | ERROR    | e99f49dff72f416f8fc2f969341ee5e6 | Exception in ASGI application

2025-08-29 10:55:40.432 | ERROR    | 4ef35468f8134e2d895223bd91db0ff4 | 请求异常: 请求参数非法: source 字段为必填项，输入：{'status_code': '200'}
2025-08-29 15:18:29.370 | ERROR    | 6c799c8ac25f439aa39e139423609e7c | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data='报告生成失败: unsupported format string passed to Undefined.__format__')}

2025-08-29 15:18:29.374 | ERROR    | 6c799c8ac25f439aa39e139423609e7c | Exception in ASGI application

2025-08-29 15:21:54.276 | ERROR    | 6bc1b3cb4cec486eb8575a05665fa2b6 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data='报告生成失败: unsupported format string passed to Undefined.__format__')}

2025-08-29 15:21:54.278 | ERROR    | 6bc1b3cb4cec486eb8575a05665fa2b6 | Exception in ASGI application

2025-08-29 15:22:37.158 | ERROR    | c4cd30306efd4de98d7d3618dfdd352c | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data='报告生成失败: unsupported format string passed to Undefined.__format__')}

2025-08-29 15:22:37.161 | ERROR    | c4cd30306efd4de98d7d3618dfdd352c | Exception in ASGI application

2025-08-29 15:22:46.920 | ERROR    | a9b1d75c9d304981825ac59b5d334a4e | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data='报告生成失败: unsupported format string passed to Undefined.__format__')}

2025-08-29 15:22:46.922 | ERROR    | a9b1d75c9d304981825ac59b5d334a4e | Exception in ASGI application

2025-08-29 15:23:36.550 | ERROR    | 2ccaf3e6a01c483cad8f6679baa50be8 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data='报告生成失败: unsupported format string passed to Undefined.__format__')}

2025-08-29 15:23:36.553 | ERROR    | 2ccaf3e6a01c483cad8f6679baa50be8 | Exception in ASGI application

2025-08-29 15:23:42.167 | ERROR    | a007d53f1e244b43b6763f794e685f6e | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data='报告生成失败: unsupported format string passed to Undefined.__format__')}

2025-08-29 15:23:42.169 | ERROR    | a007d53f1e244b43b6763f794e685f6e | Exception in ASGI application

2025-08-29 15:38:15.471 | ERROR    | 17ae7d3b30ee41fb86fc637283238ccf | 请求异常: 请求参数非法: name 字段为必填项，输入：{'report_data': {'name': '慕依诺', 'project_name': '帛艺涵', 'test_case_name': '烟雅鑫', 'description': None, 'environment': 'sint', 'success': False, 'total_steps': 46, 'success_steps': 4, 'fail_steps': 47, 'steps': [{'name': '繁晨阳', 'order': 38, 'url': 'https://wilted-wallaby.org/', 'method': 'GET', 'request_data': {}, 'response': {}, 'assertions': [], 'sql_results': [], 'variables': None, 'success': True, 'start_time': '2026-01-29 16:13:26', 'end_time': '2025-03-07 12:19:21', 'duration': 30}], 'start_time': '2026-04-08 08:12:06', 'end_time': '2026-02-07 10:16:07', 'duration': 10}, 'format': 'html'}
