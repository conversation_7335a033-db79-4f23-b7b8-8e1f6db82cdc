2025-08-28 14:09:52.796 | ERROR    | e28222f584474ca7a57dd554ebc58ee9 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-28 14:09:52.799 | ERROR    | e28222f584474ca7a57dd554ebc58ee9 | Exception in ASGI application

2025-08-28 14:12:19.670 | ERROR    | 62c2253d1b6844fea212f166b4065b8d | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'id': 'string'})}

2025-08-28 14:12:19.673 | ERROR    | 62c2253d1b6844fea212f166b4065b8d | Exception in ASGI application

2025-08-28 14:14:11.438 | ERROR    | e92e130d284544f09382dc99f180cfbe | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'id': '1'})}

2025-08-28 14:14:11.440 | ERROR    | e92e130d284544f09382dc99f180cfbe | Exception in ASGI application

2025-08-28 14:14:41.776 | ERROR    | 46d59d5403c64727839f0957d366afe5 | 请求异常: 请求参数非法: test_case_id 输入应为有效的字符串，输入：1
2025-08-28 14:14:55.801 | ERROR    | a178e4282b584ba7b3a14e8f11f47186 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'id': '1'})}

2025-08-28 14:14:55.803 | ERROR    | a178e4282b584ba7b3a14e8f11f47186 | Exception in ASGI application

2025-08-28 14:15:31.801 | ERROR    | 55991704fe524e4a98b3927fb2b16a28 | 请求异常: 请求参数非法: response_time 输入应为有效的数字，输入：{}
2025-08-28 14:16:56.193 | ERROR    | e8b66a36f662406db66ed4646d79706e | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-28 14:16:56.194 | ERROR    | e8b66a36f662406db66ed4646d79706e | Exception in ASGI application

2025-08-28 14:17:30.427 | ERROR    | eabdbf996e5c46368dec4de13af5b85a | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-28 14:17:30.428 | ERROR    | eabdbf996e5c46368dec4de13af5b85a | Exception in ASGI application

2025-08-28 14:29:45.070 | ERROR    | 44ce2dc4723a4265ae427f92bf490036 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-28 14:29:45.072 | ERROR    | 44ce2dc4723a4265ae427f92bf490036 | Exception in ASGI application

2025-08-28 14:31:38.362 | ERROR    | c5916edf09ba4a71b520f6523978c871 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'id': 'q-WKPcRvgaADpjH8c5Yyl'})}

2025-08-28 14:31:38.366 | ERROR    | c5916edf09ba4a71b520f6523978c871 | Exception in ASGI application

2025-08-28 14:39:20.261 | ERROR    | 866999b5df044567aabddc8e915371df | 请求异常: 请求参数非法: limit 输入应为有效的整数，无法将字符串解析为整数，输入：
2025-08-28 14:39:27.714 | ERROR    | 0730d369170544c8bfda44effb83958e | 请求异常: 请求参数非法: skip 输入应为有效的整数，无法将字符串解析为整数，输入：
2025-08-28 14:39:37.398 | ERROR    | 82d1dce80ecb4666a6c3e446e1db3eef | 请求异常: 请求参数非法: start_time Input should be a valid datetime or date, input is too short，输入：
2025-08-28 14:40:01.909 | ERROR    | 5720d6bc8e5340bfaec4b9a5f64f1812 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}, {'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}, {'id': 'string', 'name': 'string', 'url': 'string', 'method': 'string', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': None, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': None, 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}, {'id': '1', 'name': '1', 'url': 'www.baidu.com', 'method': 'GET', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': {}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': 200, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': None, 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}, {'id': '1', 'name': '1', 'url': 'www.baidu.com', 'method': 'GET', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': {}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': 200, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': '1', 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}], 'pagination': {'total': 5, 'limit': 100, 'skip': 0, 'page': 1, 'pages': 1}})}

2025-08-28 14:40:01.911 | ERROR    | 5720d6bc8e5340bfaec4b9a5f64f1812 | Exception in ASGI application

2025-08-28 14:40:11.973 | ERROR    | 21f58bd1d89541c98622976fa9fb00a8 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}], 'pagination': {'total': 5, 'limit': 1, 'skip': 0, 'page': 1, 'pages': 5}})}

2025-08-28 14:40:11.975 | ERROR    | 21f58bd1d89541c98622976fa9fb00a8 | Exception in ASGI application

2025-08-28 14:42:21.487 | ERROR    | c66ec5554d4546829f41dfb2a7083f42 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}], 'pagination': {'total': 5, 'limit': 1, 'skip': 0, 'page': 1, 'pages': 5}})}

2025-08-28 14:42:21.488 | ERROR    | c66ec5554d4546829f41dfb2a7083f42 | Exception in ASGI application

2025-08-28 14:42:55.544 | ERROR    | af4d5c6472364271aad8a1120f962e86 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [], 'pagination': {'total': 0, 'limit': 100, 'skip': 0, 'page': 1, 'pages': 0}})}

2025-08-28 14:42:55.545 | ERROR    | af4d5c6472364271aad8a1120f962e86 | Exception in ASGI application

2025-08-28 14:43:07.418 | ERROR    | 46376d3779e549a1925c5454555fd992 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}, {'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}, {'id': 'string', 'name': 'string', 'url': 'string', 'method': 'string', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': None, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': None, 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}, {'id': '1', 'name': '1', 'url': 'www.baidu.com', 'method': 'GET', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': {}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': 200, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': None, 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}, {'id': '1', 'name': '1', 'url': 'www.baidu.com', 'method': 'GET', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': {}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': 200, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': '1', 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}], 'pagination': {'total': 5, 'limit': 100, 'skip': 0, 'page': 1, 'pages': 1}})}

2025-08-28 14:43:07.420 | ERROR    | 46376d3779e549a1925c5454555fd992 | Exception in ASGI application

2025-08-28 14:43:10.523 | ERROR    | 2c654650e7d74f0cb423668e3b1a11ce | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}, {'id': 'q-WKPcRvgaADpjH8c5Yyl', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}, {'id': 'string', 'name': 'string', 'url': 'string', 'method': 'string', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': None, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': None, 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}, {'id': '1', 'name': '1', 'url': 'www.baidu.com', 'method': 'GET', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': {}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': 200, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': None, 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}, {'id': '1', 'name': '1', 'url': 'www.baidu.com', 'method': 'GET', 'headers': {'officia_e': 'magna qui', 'ipsum5f': 'consequat irure Ut ullamco', 'mollit_2a': 'elit'}, 'params': {}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': 200, 'response_headers': {'do33': 'aliqua laboris commodo pariatur nisi'}, 'response_body': {'title': 'Response Body', 'description': '响应体'}, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 11, 50, 704000, tzinfo=datetime.timezone.utc), 'project_id': None, 'environment_id': 'nisi sint velit magna', 'test_case_id': '1', 'test_step_id': 'velit veniam occaecat adipisicing aute', 'assertions': None, 'error': None, 'tags': ['string']}], 'pagination': {'total': 5, 'limit': 100, 'skip': 0, 'page': 1, 'pages': 1}})}

2025-08-28 14:43:10.526 | ERROR    | 2c654650e7d74f0cb423668e3b1a11ce | Exception in ASGI application

2025-08-28 14:44:00.027 | ERROR    | 75bd080d2be54a629c43909b3b18f66e | 请求异常: 请求参数非法: project_id 输入应为有效的字符串，输入：1
2025-08-28 14:44:37.553 | ERROR    | add7e9a1578c43c08b646c55952f8c62 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': '1', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=TzInfo(UTC)), 'project_id': '1', 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}], 'pagination': {'total': 1, 'limit': 100, 'skip': 0, 'page': 1, 'pages': 1}})}

2025-08-28 14:44:37.554 | ERROR    | add7e9a1578c43c08b646c55952f8c62 | Exception in ASGI application

2025-08-28 14:45:35.930 | ERROR    | ad27f2b1981743f290a5cc33b32644db | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [{'id': '1', 'name': '夕涛', 'url': 'https://puzzled-transom.name/', 'method': 'eiusmod laboris cillum elit', 'headers': {'velit__f': 'qui'}, 'params': {'Lorem2': 'eiusmod irure sint', 'ex_0': True, 'proident_9': -35673098}, 'body': {'title': 'Body', 'description': '请求体'}, 'status_code': None, 'response_headers': None, 'response_body': None, 'response_time': None, 'timestamp': datetime.datetime(2025, 8, 28, 6, 31, 36, tzinfo=datetime.timezone.utc), 'project_id': '1', 'environment_id': 'in minim', 'test_case_id': 'velit', 'test_step_id': None, 'assertions': None, 'error': 'et', 'tags': ['commodo sint est', 'officia et adipisicing anim nostrud']}], 'pagination': {'total': 1, 'limit': 100, 'skip': 0, 'page': 1, 'pages': 1}})}

2025-08-28 14:45:35.932 | ERROR    | ad27f2b1981743f290a5cc33b32644db | Exception in ASGI application

2025-08-28 14:49:20.430 | ERROR    | 9270661db68944a2a74b294ec95d482c | 请求异常: 请求参数非法: body 字段为必填项，输入：None
2025-08-28 14:49:37.760 | ERROR    | 8f51d319a99240db891b4a12b657b771 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'items': [], 'pagination': {'total': 0, 'limit': 1, 'skip': 1, 'page': 2, 'pages': 0}, 'filter': {'project_id': '1', 'start_time': None, 'end_time': None, 'url_contains': None, 'method': 'esse nostrud consectetur do', 'tags': None, 'successful': False}})}

2025-08-28 14:49:37.761 | ERROR    | 8f51d319a99240db891b4a12b657b771 | Exception in ASGI application

2025-08-28 17:31:00.721 | ERROR    | 72978c7db6f441f989de5419eccff487 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'url': 'http://127.0.0.1:8000/api/v1/auth/login/swagger', 'method': 'POST', 'status_code': 0, 'elapsed_time': 30197.80683517456, 'headers': {}, 'cookies': {}, 'content': '', 'text': '', 'json_data': None, 'error': ''})}

2025-08-28 17:31:00.725 | ERROR    | 72978c7db6f441f989de5419eccff487 | Exception in ASGI application

2025-08-28 17:31:54.761 | ERROR    | 6ec14002f45a41de92763ae69c2742e3 | 请求异常: 2 validation errors:
  {'type': 'missing', 'loc': ('response', 'url'), 'msg': 'Field required', 'input': ResponseModel(code=200, msg='请求成功', data={'url': 'http://127.0.0.1:8000/api/v1/auth/login/swagger?username=admin&password=123456', 'method': 'POST', 'status_code': 502, 'elapsed_time': 16143.599271774292, 'headers': {'connection': 'close', 'content-length': '0'}, 'cookies': {}, 'content': '', 'text': '', 'json_data': None, 'error': None})}
  {'type': 'missing', 'loc': ('response', 'method'), 'msg': 'Field required', 'input': ResponseModel(code=200, msg='请求成功', data={'url': 'http://127.0.0.1:8000/api/v1/auth/login/swagger?username=admin&password=123456', 'method': 'POST', 'status_code': 502, 'elapsed_time': 16143.599271774292, 'headers': {'connection': 'close', 'content-length': '0'}, 'cookies': {}, 'content': '', 'text': '', 'json_data': None, 'error': None})}

2025-08-28 17:31:54.763 | ERROR    | 6ec14002f45a41de92763ae69c2742e3 | Exception in ASGI application

2025-08-28 17:33:28.634 | ERROR    | 08471579da7849578d66ff00a113b24c | 请求异常: 2 validation errors:
  {'type': 'missing', 'loc': ('response', 'url'), 'msg': 'Field required', 'input': ResponseModel(code=200, msg='请求成功', data={'url': 'http://127.0.0.1:8000/api/v1/auth/login/swagger', 'method': 'POST', 'status_code': 0, 'elapsed_time': 30064.730882644653, 'headers': {}, 'cookies': {}, 'content': '', 'text': '', 'json_data': None, 'error': ''})}
  {'type': 'missing', 'loc': ('response', 'method'), 'msg': 'Field required', 'input': ResponseModel(code=200, msg='请求成功', data={'url': 'http://127.0.0.1:8000/api/v1/auth/login/swagger', 'method': 'POST', 'status_code': 0, 'elapsed_time': 30064.730882644653, 'headers': {}, 'cookies': {}, 'content': '', 'text': '', 'json_data': None, 'error': ''})}

2025-08-28 17:33:28.636 | ERROR    | 08471579da7849578d66ff00a113b24c | Exception in ASGI application

2025-08-28 18:02:22.194 | ERROR    | fb7a297f7b5b44388b9bab76ca123944 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'url': 'http://127.0.0.1:8000/api/v1/auth/login/swagger?username=admin&password=123456', 'method': 'POST', 'status_code': 200, 'elapsed_time': 518.8205242156982, 'headers': {'connection': 'close', 'content-length': '617', 'content-type': 'application/json', 'date': 'Thu, 28 Aug 2025 10:02:20 GMT', 'server': 'uvicorn', 'x-request-id': 'abf2fcf4ba3d49c0bdda52a1a8e72f15'}, 'cookies': {}, 'content': '{"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uX3V1aWQiOiJmZDRkNzA3NS04NjMyLTQ2OTctYWI4OC04ODg1OTA2NjRjN2EiLCJleHAiOjE3NTY0NjE3NDIuMTgzOTM5LCJzdWIiOiIyMDQ4NjAxMjYzODM0MjY3NjQ4In0.rPdOAzgiOoVb-WV616QyphDQxKbU--xz9SurXeEQfo4","token_type":"Bearer","user":{"dept_id":2048601258595581952,"username":"admin","nickname":"用户88888","avatar":null,"email":"<EMAIL>","phone":null,"id":2048601263834267648,"uuid":"9f2ec04b-6222-11f0-98e0-1c872cd0fdd5","status":1,"is_superuser":true,"is_staff":true,"is_multi_login":true,"join_time":"2025-07-16 16:55:28","last_login_time":"2025-08-28 18:02:22"}}', 'text': '{"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uX3V1aWQiOiJmZDRkNzA3NS04NjMyLTQ2OTctYWI4OC04ODg1OTA2NjRjN2EiLCJleHAiOjE3NTY0NjE3NDIuMTgzOTM5LCJzdWIiOiIyMDQ4NjAxMjYzODM0MjY3NjQ4In0.rPdOAzgiOoVb-WV616QyphDQxKbU--xz9SurXeEQfo4","token_type":"Bearer","user":{"dept_id":2048601258595581952,"username":"admin","nickname":"用户88888","avatar":null,"email":"<EMAIL>","phone":null,"id":2048601263834267648,"uuid":"9f2ec04b-6222-11f0-98e0-1c872cd0fdd5","status":1,"is_superuser":true,"is_staff":true,"is_multi_login":true,"join_time":"2025-07-16 16:55:28","last_login_time":"2025-08-28 18:02:22"}}', 'json_data': {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uX3V1aWQiOiJmZDRkNzA3NS04NjMyLTQ2OTctYWI4OC04ODg1OTA2NjRjN2EiLCJleHAiOjE3NTY0NjE3NDIuMTgzOTM5LCJzdWIiOiIyMDQ4NjAxMjYzODM0MjY3NjQ4In0.rPdOAzgiOoVb-WV616QyphDQxKbU--xz9SurXeEQfo4', 'token_type': 'Bearer', 'user': {'dept_id': 2048601258595581952, 'username': 'admin', 'nickname': '用户88888', 'avatar': None, 'email': '<EMAIL>', 'phone': None, 'id': 2048601263834267648, 'uuid': '9f2ec04b-6222-11f0-98e0-1c872cd0fdd5', 'status': 1, 'is_superuser': True, 'is_staff': True, 'is_multi_login': True, 'join_time': '2025-07-16 16:55:28', 'last_login_time': '2025-08-28 18:02:22'}}, 'error': None})}

2025-08-28 18:02:22.196 | ERROR    | fb7a297f7b5b44388b9bab76ca123944 | Exception in ASGI application

