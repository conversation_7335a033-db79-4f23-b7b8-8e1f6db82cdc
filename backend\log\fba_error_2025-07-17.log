2025-07-16 10:47:19.489 | ERROR    | - | Traceback (most recent call last):
  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 70, in serve
    with self.capture_signals():
  File "D:\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "D:\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-16 10:48:27.259 | ERROR    | a706fc53004d44eba01c561f8f5a0c43 | 请求异常: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-16 10:48:27.261 | ERROR    | a706fc53004d44eba01c561f8f5a0c43 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x0000021F209255A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x0000021F20881900>
           └ <SpawnProcess name='SpawnProcess-1' parent=17472 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000021F20880F70>
    └ <SpawnProcess name='SpawnProcess-1' parent=17472 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000021F209162F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=17472 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=17472 started>
    │    └ <function subprocess_started at 0x0000021F21161510>
    └ <SpawnProcess name='SpawnProcess-1' parent=17472 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=728, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000021F20916410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=728, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000021F2113DD80>
           │       │   └ <uvicorn.server.Server object at 0x0000021F20916410>
           │       └ <function run at 0x0000021F20926680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000021F21245AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000021F20AB6050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000021F20AB5FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000021F20AB7AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000021F20A1F490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000021F21120940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021F27...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000021F25825B70>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000021F21120940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021F27...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021F27...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000021F27947790>
          └ <fastapi.applications.FastAPI object at 0x0000021F25825B70>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000021F27D82DD0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x0000021F279471C0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000021F27947790>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000021F27D830A0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000021F279471C0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x0000021F279471C0>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000021F22B64C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000021F27DC2E00>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('the JSON object must be str, bytes or bytearray, not NoneType')])
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000021F27D6A030>
    └ <contextlib._GeneratorContextManager object at 0x0000021F27DBA0B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000021F27D83130>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000021F27DB9F30>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x0000021F279471C0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000021F279471C0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000021F27DB9F30>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000021F27D83130>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000021F27D82EF0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D82F80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000021F27947160>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000021F279471C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '0', 'sec-ch-ua-platform': '"Windows"', 'aut...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000021F27D82EF0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D82F80>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000021F22C8C790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000021F27947160>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000021F27947160...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D82F80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000021F27947130>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000021F27947160>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000021F27947160...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D82F80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000021F279470D0>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000021F27947130>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000021F22B64C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000021F27DD6800>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('the JSON object must be str, bytes or bytearray, not NoneType')])
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000021F27D693F0>
    └ <contextlib._GeneratorContextManager object at 0x0000021F27DBAD10>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000021F27D83370>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000021F27DBAB30>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x0000021F279470D0>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000021F279470D0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000021F27DBAB30>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000021F27D83370>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000021F27D832E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D83400>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000021F27946FB0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000021F279470D0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000021F22B64C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000021F27DD5D00>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('the JSON object must be str, bytes or bytearray, not NoneType')])
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000021F27D6A650>
    └ <contextlib._GeneratorContextManager object at 0x0000021F27DBB430>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000021F27D83640>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000021F27DBB250>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000021F...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000021F27946FB0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000021F27DBB250>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000021F27D83640>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000021F27D83910>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000021F279470A0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000021F27946FB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000021F27D83910>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000021F27DBB820>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000021F25825C30>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000021F279470A0>
          └ <function wrap_app_handling_exceptions at 0x0000021F22BD68C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000021F27D83A30>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000021F25825C30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000021F27D83A30>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000021F25825C30>>
          └ <fastapi.routing.APIRouter object at 0x0000021F25825C30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000021F27D83A30>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000021F22C000D0>
          └ APIRoute(path='/api/v1/task/schedulers/{pk}/executions', name='execute_task', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000021F27D83A30>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000021F2786AE60>
          └ APIRoute(path='/api/v1/task/schedulers/{pk}/executions', name='execute_task', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000021F27D83A30>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000021F27DBBA60>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000021F27D83AC0>
          └ <function wrap_app_handling_exceptions at 0x0000021F22BD68C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000021F27D83B50>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000021F27D839A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000021F27D83AC0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000021F27DBBA60>
                     └ <function get_request_handler.<locals>.app at 0x0000021F2786ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000021F22C01BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'pk': 2}
                 │         └ <function execute_task at 0x0000021F272908B0>
                 └ Dependant(path_params=[ModelField(field_info=Path(PydanticUndefined), name='pk', mode='validation')], query_params=[], header...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\scheduler.py", line 120, in execute_task
    await task_scheduler_service.execute(pk=pk)
          │                      │          └ 2
          │                      └ <staticmethod(<function TaskSchedulerService.execute at 0x0000021F271BFB50>)>
          └ <backend.app.task.service.scheduler_service.TaskSchedulerService object at 0x0000021F27297DF0>

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\service\scheduler_service.py", line 154, in execute
    kwargs=json.loads(task_scheduler.kwargs),
           │    │     │              └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x0000021F252051C0>
           │    │     └ TaskScheduler(created_time=datetime.datetime(2025, 7, 15, 16, 56, 56), updated_time=datetime.datetime(2025, 7, 16, 10, 48, 25...
           │    └ <function loads at 0x0000021F1EC81D80>
           └ <module 'json' from 'D:\\Python310\\lib\\json\\__init__.py'>

  File "D:\Python310\lib\json\__init__.py", line 339, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '

TypeError: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-16 11:14:27.414 | ERROR    | - | Traceback (most recent call last):
  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 70, in serve
    with self.capture_signals():
  File "D:\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "D:\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-16 11:42:46.328 | ERROR    | 9ea96a418f6d44bea74980995b3b68db | 请求异常: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-16 11:42:46.332 | ERROR    | 9ea96a418f6d44bea74980995b3b68db | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 216
               │     └ 3
               └ <function _main at 0x000001BEC50B55A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 216
           │    └ <function BaseProcess._bootstrap at 0x000001BEC5011900>
           └ <SpawnProcess name='SpawnProcess-4' parent=26548 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001BEC5010F70>
    └ <SpawnProcess name='SpawnProcess-4' parent=26548 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001BEC50A62F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-4' parent=26548 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-4' parent=26548 started>
    │    └ <function subprocess_started at 0x000001BEC58F1510>
    └ <SpawnProcess name='SpawnProcess-4' parent=26548 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001BEC50A6410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001BEC58CDD80>
           │       │   └ <uvicorn.server.Server object at 0x000001BEC50A6410>
           │       └ <function run at 0x000001BEC50B6680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001BEC59D9AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001BEC5246050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001BEC5245FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BEC5247AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BEC51AF490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BEC58B0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BECC...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001BEC9FC5A80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BEC58B0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BECC...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BECC...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BECC0D37F0>
          └ <fastapi.applications.FastAPI object at 0x000001BEC9FC5A80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001BECC343B50>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BECC0D3220>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BECC0D37F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001BECC343E20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BECC0D3220>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BECC0D3220>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BEC7304C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001BECC50F700>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('the JSON object must be str, bytes or bytearray, not NoneType')])
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BECC2D78B0>
    └ <contextlib._GeneratorContextManager object at 0x000001BECC372350>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BECC343EB0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BECC371F30>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BECC0D3220>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BECC0D3220>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BECC371F30>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BECC343EB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BECC343C70>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC343D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BECC0D31C0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BECC0D3220>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '0', 'sec-ch-ua-platform': '"Windows"', 'aut...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BECC343C70>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC343D00>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001BEC742C790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BECC0D31C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BECC0D31C0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC343D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BECC0D3190>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BECC0D31C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BECC0D31C0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC343D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BECC0D3130>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BECC0D3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BEC7304C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001BECC50EB40>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('the JSON object must be str, bytes or bytearray, not NoneType')])
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BECC2D7610>
    └ <contextlib._GeneratorContextManager object at 0x000001BECC372FB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BECC39C160>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BECC372DD0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001BECC0D3130>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BECC0D3130>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BECC372DD0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BECC39C160>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BECC39C0D0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C1F0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BECC0D3010>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BECC0D3130>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BEC7304C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001BECC50EC40>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('the JSON object must be str, bytes or bytearray, not NoneType')])
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BECC3A04A0>
    └ <contextlib._GeneratorContextManager object at 0x000001BECC3736D0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BECC39C430>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BECC3734F0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BE...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BECC0D3010>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BECC3734F0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BECC39C430>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ TypeError('the JSON object must be str, bytes or bytearray, not NoneType')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BECC39C790>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BECC0D3100>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BECC0D3010>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BECC39C790>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BECC373E20>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BEC9FC5CC0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BECC0D3100>
          └ <function wrap_app_handling_exceptions at 0x000001BEC73768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BECC39C940>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BEC9FC5CC0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BECC39C940>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BEC9FC5CC0>>
          └ <fastapi.routing.APIRouter object at 0x000001BEC9FC5CC0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BECC39C940>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001BEC73A00D0>
          └ APIRoute(path='/api/v1/task/schedulers/{pk}/executions', name='execute_task', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BECC39C940>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BECBFF6DD0>
          └ APIRoute(path='/api/v1/task/schedulers/{pk}/executions', name='execute_task', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BECC39C940>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BECC373F70>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BECC39C9D0>
          └ <function wrap_app_handling_exceptions at 0x000001BEC73768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BECC39CA60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BECC39C820>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BECC39C9D0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BECC373F70>
                     └ <function get_request_handler.<locals>.app at 0x000001BECBFF6D40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001BEC73A1BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'pk': 1}
                 │         └ <function execute_task at 0x000001BECBA2C8B0>
                 └ Dependant(path_params=[ModelField(field_info=Path(PydanticUndefined), name='pk', mode='validation')], query_params=[], header...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\scheduler.py", line 120, in execute_task
    await task_scheduler_service.execute(pk=pk)
          │                      │          └ 1
          │                      └ <staticmethod(<function TaskSchedulerService.execute at 0x000001BECB953B50>)>
          └ <backend.app.task.service.scheduler_service.TaskSchedulerService object at 0x000001BECBA2BE80>

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\service\scheduler_service.py", line 153, in execute
    args=json.loads(task_scheduler.args),
         │    │     │              └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001BEC9995120>
         │    │     └ TaskScheduler(created_time=datetime.datetime(2025, 7, 15, 16, 52, 19), updated_time=datetime.datetime(2025, 7, 16, 11, 42, 33...
         │    └ <function loads at 0x000001BEC3421D80>
         └ <module 'json' from 'D:\\Python310\\lib\\json\\__init__.py'>

  File "D:\Python310\lib\json\__init__.py", line 339, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '

TypeError: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-16 16:34:30.138 | ERROR    | d35b6659e4c447d1819d0540f3176046 | 请求异常: Refresh Token 已过期，请重新登录
2025-07-16 16:44:30.520 | ERROR    | 0c17963854e64c7e8d709973b770b56c | 请求异常: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:44:30.522 | ERROR    | 0c17963854e64c7e8d709973b770b56c | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x00000232065C55A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x0000023206521900>
           └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000023206520F70>
    └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000232065B62F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>
    │    └ <function subprocess_started at 0x0000023206E01510>
    └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=512, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000232065B6410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=512, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000023206DDDD80>
           │       │   └ <uvicorn.server.Server object at 0x00000232065B6410>
           │       └ <function run at 0x00000232065C6680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000023206EE9AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000023206756050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000023206755FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000023206757AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000232066BF490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023206DC0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002320B4C9B10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023206DC0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002320D5E37C0>
          └ <fastapi.applications.FastAPI object at 0x000002320B4C9B10>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002320DA52EF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002320D5E37C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002320DA530A0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DA8EA00>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DA07530>
    └ <contextlib._GeneratorContextManager object at 0x000002320DABD060>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA52B00>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320DAA3280>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320DAA3280>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA52B00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51120>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52C20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51120>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52C20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002320892C790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52C20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002320D5E3160>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52C20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002320D5E3160>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DA8D040>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DA06C70>
    └ <contextlib._GeneratorContextManager object at 0x000002320DABD1B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA516C0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320DABC550>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320DABC550>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA516C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51CF0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA50A60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DABB880>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DAB4C10>
    └ <contextlib._GeneratorContextManager object at 0x000002320DABC760>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA50700>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320DABD6F0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000232...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320DABD6F0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA50700>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51990>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002320D5E30D0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51990>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002320DABDD80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002320D5E30D0>
          └ <function wrap_app_handling_exceptions at 0x00000232088768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DA52950>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DA52950>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002320B4C9D50>>
          └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DA52950>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000232088A00D0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DA52950>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002320D5025F0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DA52950>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002320DABDED0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002320DA529E0>
          └ <function wrap_app_handling_exceptions at 0x00000232088768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DA52F80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52560>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002320DA529E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002320DABDED0>
                     └ <function get_request_handler.<locals>.app at 0x000002320D502710>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000232088A1BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DABE2F0>, 'name': None, 'task_id': None}
                 │         └ <function get_task_results_paged at 0x000002320CE62950>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='name', mode='validation'), ModelField(field_...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\result.py", line 41, in get_task_results_paged
    page_data = await paging_data(db, result_select)
                      │           │   └ <sqlalchemy.sql.selectable.Select object at 0x000002320DABE890>
                      │           └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DABE2F0>
                      └ <function paging_data at 0x000002320B507EB0>

  File "F:\gitpush\fastapi_best_architecture\backend\common\pagination.py", line 122, in paging_data
    paginated_data: _CustomPage = await apaginate(db, select)
                                        │         │   └ <sqlalchemy.sql.selectable.Select object at 0x000002320DABE890>
                                        │         └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DABE2F0>
                                        └ <function apaginate at 0x000002320B549510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 552, in apaginate
    return await run_async_flow(
                 └ <function run_async_flow at 0x00000232088E6830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 72, in run_async_flow
    res = gen.throw(exc)
          │   └ <method 'throw' of 'generator' objects>
          └ <generator object _sqlalchemy_flow at 0x000002320DAB5070>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 323, in _sqlalchemy_flow
    page = yield from generic_flow(
                      └ <function generic_flow at 0x00000232088E6D40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flows.py", line 113, in generic_flow
    total = yield from total_flow()
                       └ functools.partial(<function _total_flow at 0x000002320B549000>, <sqlalchemy.sql.selectable.Select object at 0x000002320DABE89...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 260, in _total_flow
    total = yield conn.scalar(count_query)
                  │    │      └ <sqlalchemy.sql.selectable.Select object at 0x000002320DABF100>
                  │    └ <function AsyncSession.scalar at 0x000002320A4B1C60>
                  └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DABE2F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 67, in run_async_flow
    res = await await_if_coro(res)
                │             └ <coroutine object AsyncSession.scalar at 0x000002320DAB5230>
                └ <function await_if_coro at 0x00000232088BB640>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\utils.py", line 100, in await_if_coro
    return cast(R, await coro)
           │    │        └ <coroutine object AsyncSession.scalar at 0x000002320DAB5230>
           │    └ ~R
           └ <function cast at 0x0000023206725900>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 519, in scalar
    return await greenlet_spawn(
                 └ <function greenlet_spawn at 0x0000023208A5A170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000002320DA77600 (otid=0x0000023208A2C5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2413, in scalar
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000002320A23D750>
           └ <sqlalchemy.orm.session.Session object at 0x000002320DABE6B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000002320A0C39A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x0000023209F051B0>
             └ <sqlalchemy.engine.base.Connection object at 0x000002320DAA3580>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000002320DABF100>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x0000023209F05480>
           └ <sqlalchemy.engine.base.Connection object at 0x000002320DAA3580>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x0000023209F05630>
          └ <sqlalchemy.engine.base.Connection object at 0x000002320DAA3580>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x0000023209F056C0>
           └ <sqlalchemy.engine.base.Connection object at 0x000002320DAA3580>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x0000023209F05900>
    └ <sqlalchemy.engine.base.Connection object at 0x000002320DAA3580>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'fba.task_result' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'fba.task_result' doesn't exist"), <traceback objec...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000023209FB5480>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002320A4E5E40>
    └ <sqlalchemy.engine.base.Connection object at 0x000002320DAA3580>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ()
    │      │       └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002320A576CB0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320D7C3650>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ()
           │    │      │    │              └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320A576DD0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320D7C3650>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320D7C3650>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320D68CCF0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000002320DA77600 (otid=0x0000023208A2C5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320D68CCF0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ()
                   │    │               └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320D7C3650>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000002320A545BE0>
    └ <module 'asyncmy.errors' from 'F:\\gitpush\\fastapi_best_architecture\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_a...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000002320A545BE0>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:50:09.217 | ERROR    | 345c93ccd9e042e38690f8385dcb4da7 | 请求异常: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:50:09.220 | ERROR    | 345c93ccd9e042e38690f8385dcb4da7 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x00000232065C55A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x0000023206521900>
           └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000023206520F70>
    └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000232065B62F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>
    │    └ <function subprocess_started at 0x0000023206E01510>
    └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=512, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000232065B6410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=512, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000023206DDDD80>
           │       │   └ <uvicorn.server.Server object at 0x00000232065B6410>
           │       └ <function run at 0x00000232065C6680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000023206EE9AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000023206756050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000023206755FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000023206757AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000232066BF490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023206DC0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002320B4C9B10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023206DC0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002320D5E37C0>
          └ <fastapi.applications.FastAPI object at 0x000002320B4C9B10>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002320D9F0280>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002320D5E37C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002320D6980D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DAAC100>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320D5230D0>
    └ <contextlib._GeneratorContextManager object at 0x000002320DAA1C90>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA51AB0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320DAA1C30>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320DAA1C30>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DA51AB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51EA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DA51EA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52200>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002320892C790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002320D5E3160>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DA52200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002320D5E3160>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DA9AF40>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DAB4900>
    └ <contextlib._GeneratorContextManager object at 0x000002320DAA28F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3D7E0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320DAA29B0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320DAA29B0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3D7E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3D750>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3D990>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DE5CB00>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DBCA880>
    └ <contextlib._GeneratorContextManager object at 0x000002320DA64100>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3DBD0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320DA64610>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000232...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320DA64610>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3DBD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3DF30>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002320D5E30D0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3DF30>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002320DA654E0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002320D5E30D0>
          └ <function wrap_app_handling_exceptions at 0x00000232088768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3E050>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3E050>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002320B4C9D50>>
          └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3E050>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000232088A00D0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3E050>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002320D5025F0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3E050>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002320DA67670>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002320DE3E0E0>
          └ <function wrap_app_handling_exceptions at 0x00000232088768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3E170>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3DFC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002320DE3E0E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002320DA67670>
                     └ <function get_request_handler.<locals>.app at 0x000002320D502710>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000232088A1BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DA65DB0>, 'name': None, 'task_id': None}
                 │         └ <function get_task_results_paged at 0x000002320CE62950>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='name', mode='validation'), ModelField(field_...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\result.py", line 41, in get_task_results_paged
    page_data = await paging_data(db, result_select)
                      │           │   └ <sqlalchemy.sql.selectable.Select object at 0x000002320D67C5E0>
                      │           └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DA65DB0>
                      └ <function paging_data at 0x000002320B507EB0>

  File "F:\gitpush\fastapi_best_architecture\backend\common\pagination.py", line 122, in paging_data
    paginated_data: _CustomPage = await apaginate(db, select)
                                        │         │   └ <sqlalchemy.sql.selectable.Select object at 0x000002320D67C5E0>
                                        │         └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DA65DB0>
                                        └ <function apaginate at 0x000002320B549510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 552, in apaginate
    return await run_async_flow(
                 └ <function run_async_flow at 0x00000232088E6830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 72, in run_async_flow
    res = gen.throw(exc)
          │   └ <method 'throw' of 'generator' objects>
          └ <generator object _sqlalchemy_flow at 0x000002320DE45EE0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 323, in _sqlalchemy_flow
    page = yield from generic_flow(
                      └ <function generic_flow at 0x00000232088E6D40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flows.py", line 113, in generic_flow
    total = yield from total_flow()
                       └ functools.partial(<function _total_flow at 0x000002320B549000>, <sqlalchemy.sql.selectable.Select object at 0x000002320D67C5E...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 260, in _total_flow
    total = yield conn.scalar(count_query)
                  │    │      └ <sqlalchemy.sql.selectable.Select object at 0x000002320DA373D0>
                  │    └ <function AsyncSession.scalar at 0x000002320A4B1C60>
                  └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DA65DB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 67, in run_async_flow
    res = await await_if_coro(res)
                │             └ <coroutine object AsyncSession.scalar at 0x000002320DE460A0>
                └ <function await_if_coro at 0x00000232088BB640>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\utils.py", line 100, in await_if_coro
    return cast(R, await coro)
           │    │        └ <coroutine object AsyncSession.scalar at 0x000002320DE460A0>
           │    └ ~R
           └ <function cast at 0x0000023206725900>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 519, in scalar
    return await greenlet_spawn(
                 └ <function greenlet_spawn at 0x0000023208A5A170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000002320DAC4700 (otid=0x0000023208A2C5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2413, in scalar
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000002320A23D750>
           └ <sqlalchemy.orm.session.Session object at 0x000002320D67C880>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000002320A0C39A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x0000023209F051B0>
             └ <sqlalchemy.engine.base.Connection object at 0x000002320DA374F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000002320DA373D0>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x0000023209F05480>
           └ <sqlalchemy.engine.base.Connection object at 0x000002320DA374F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x0000023209F05630>
          └ <sqlalchemy.engine.base.Connection object at 0x000002320DA374F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x0000023209F056C0>
           └ <sqlalchemy.engine.base.Connection object at 0x000002320DA374F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x0000023209F05900>
    └ <sqlalchemy.engine.base.Connection object at 0x000002320DA374F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'fba.task_result' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'fba.task_result' doesn't exist"), <traceback objec...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000023209FB5480>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002320A4E5E40>
    └ <sqlalchemy.engine.base.Connection object at 0x000002320DA374F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ()
    │      │       └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002320A576CB0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DB9AC00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ()
           │    │      │    │              └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320A576DD0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DB9AC00>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DB9AC00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320DCE7060>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000002320DAC4700 (otid=0x0000023208A2C5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320DCE7060>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ()
                   │    │               └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DB9AC00>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000002320A545BE0>
    └ <module 'asyncmy.errors' from 'F:\\gitpush\\fastapi_best_architecture\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_a...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000002320A545BE0>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:52:58.561 | ERROR    | b1a8f8829af4401a93b8f294f1265f97 | 请求异常: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:52:58.564 | ERROR    | b1a8f8829af4401a93b8f294f1265f97 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x00000232065C55A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x0000023206521900>
           └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000023206520F70>
    └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000232065B62F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>
    │    └ <function subprocess_started at 0x0000023206E01510>
    └ <SpawnProcess name='SpawnProcess-1' parent=16432 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=512, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000232065B6410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=512, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000023206DDDD80>
           │       │   └ <uvicorn.server.Server object at 0x00000232065B6410>
           │       └ <function run at 0x00000232065C6680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000023206EE9AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000023206756050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000023206755FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000023206757AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000232066BF490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023206DC0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002320B4C9B10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023206DC0940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002320D...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002320D5E37C0>
          └ <fastapi.applications.FastAPI object at 0x000002320B4C9B10>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002320DE3E680>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002320D5E37C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002320DE3DEA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000023...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DE69180>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320D523760>
    └ <contextlib._GeneratorContextManager object at 0x000002320D897190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3E710>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320D897D90>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320D897D90>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3E710>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3E560>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002320D5E31F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3E560>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3E4D0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002320892C790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002320D5E3160>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002320D5E3190...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002320D5E3160>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DE69280>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DE45A10>
    └ <contextlib._GeneratorContextManager object at 0x000002320D8D9420>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3E9E0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320D8D8F70>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320D8D8F70>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3E9E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3DCF0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3E8C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002320D5E3100>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000023208804C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002320DE90F80>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002320DE463B0>
    └ <contextlib._GeneratorContextManager object at 0x000002320D6229B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3ECB0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002320D623EB0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000232...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002320D623EB0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002320DE3ECB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3F010>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002320D5E30D0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002320D5E2FE0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002320DE3F010>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002320DAE8250>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002320D5E30D0>
          └ <function wrap_app_handling_exceptions at 0x00000232088768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3F130>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3F130>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002320B4C9D50>>
          └ <fastapi.routing.APIRouter object at 0x000002320B4C9D50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3F130>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000232088A00D0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3F130>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002320D5025F0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3F130>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002320DAE8430>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002320DE3F1C0>
          └ <function wrap_app_handling_exceptions at 0x00000232088768C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002320DE3F250>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002320DE3F0A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002320DE3F1C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002320DAE8430>
                     └ <function get_request_handler.<locals>.app at 0x000002320D502710>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000232088A1BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DAE8880>, 'name': None, 'task_id': None}
                 │         └ <function get_task_results_paged at 0x000002320CE62950>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='name', mode='validation'), ModelField(field_...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\result.py", line 41, in get_task_results_paged
    page_data = await paging_data(db, result_select)
                      │           │   └ <sqlalchemy.sql.selectable.Select object at 0x000002320DAE8CD0>
                      │           └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DAE8880>
                      └ <function paging_data at 0x000002320B507EB0>

  File "F:\gitpush\fastapi_best_architecture\backend\common\pagination.py", line 122, in paging_data
    paginated_data: _CustomPage = await apaginate(db, select)
                                        │         │   └ <sqlalchemy.sql.selectable.Select object at 0x000002320DAE8CD0>
                                        │         └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DAE8880>
                                        └ <function apaginate at 0x000002320B549510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 552, in apaginate
    return await run_async_flow(
                 └ <function run_async_flow at 0x00000232088E6830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 72, in run_async_flow
    res = gen.throw(exc)
          │   └ <method 'throw' of 'generator' objects>
          └ <generator object _sqlalchemy_flow at 0x000002320DE46F10>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 323, in _sqlalchemy_flow
    page = yield from generic_flow(
                      └ <function generic_flow at 0x00000232088E6D40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flows.py", line 113, in generic_flow
    total = yield from total_flow()
                       └ functools.partial(<function _total_flow at 0x000002320B549000>, <sqlalchemy.sql.selectable.Select object at 0x000002320DAE8CD...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 260, in _total_flow
    total = yield conn.scalar(count_query)
                  │    │      └ <sqlalchemy.sql.selectable.Select object at 0x000002320DAE8EE0>
                  │    └ <function AsyncSession.scalar at 0x000002320A4B1C60>
                  └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002320DAE8880>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 67, in run_async_flow
    res = await await_if_coro(res)
                │             └ <coroutine object AsyncSession.scalar at 0x000002320DE46570>
                └ <function await_if_coro at 0x00000232088BB640>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\utils.py", line 100, in await_if_coro
    return cast(R, await coro)
           │    │        └ <coroutine object AsyncSession.scalar at 0x000002320DE46570>
           │    └ ~R
           └ <function cast at 0x0000023206725900>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 519, in scalar
    return await greenlet_spawn(
                 └ <function greenlet_spawn at 0x0000023208A5A170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000002320DE59A40 (otid=0x0000023208A2C5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2413, in scalar
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000002320A23D750>
           └ <sqlalchemy.orm.session.Session object at 0x000002320DAE8C40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000002320A0C39A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x0000023209F051B0>
             └ <sqlalchemy.engine.base.Connection object at 0x000002320DAE9120>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000002320DAE8EE0>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x0000023209F05480>
           └ <sqlalchemy.engine.base.Connection object at 0x000002320DAE9120>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x0000023209F05630>
          └ <sqlalchemy.engine.base.Connection object at 0x000002320DAE9120>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x0000023209F056C0>
           └ <sqlalchemy.engine.base.Connection object at 0x000002320DAE9120>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x0000023209F05900>
    └ <sqlalchemy.engine.base.Connection object at 0x000002320DAE9120>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'fba.task_result' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'fba.task_result' doesn't exist"), <traceback objec...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000023209FB5480>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002320A4E5E40>
    └ <sqlalchemy.engine.base.Connection object at 0x000002320DAE9120>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ()
    │      │       └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002320A576CB0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DE6CCC0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ()
           │    │      │    │              └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320A576DD0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DE6CCC0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DE6CCC0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320DE471B0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000002320DE59A40 (otid=0x0000023208A2C5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002320DE471B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ()
                   │    │               └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002320DE6CCC0>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000002320A545BE0>
    └ <module 'asyncmy.errors' from 'F:\\gitpush\\fastapi_best_architecture\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_a...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000002320A545BE0>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:56:42.797 | ERROR    | 15037b681f6e4380843291090712c459 | 请求异常: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:56:42.800 | ERROR    | 15037b681f6e4380843291090712c459 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 328
               │     └ 3
               └ <function _main at 0x000001E2BA3755A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 328
           │    └ <function BaseProcess._bootstrap at 0x000001E2BA2D1900>
           └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E2BA2D0F70>
    └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E2BA36A2F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>
    │    └ <function subprocess_started at 0x000001E2BABB1510>
    └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=396, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E2BA36A410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=396, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E2BAB8DD80>
           │       │   └ <uvicorn.server.Server object at 0x000001E2BA36A410>
           │       └ <function run at 0x000001E2BA376680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001E2BAC95AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001E2BA506050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E2BA505FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E2BA507AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E2BA46F490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E2BAB70940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E2C1...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001E2BF27DC00>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E2BAB70940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E2C1...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E2C1...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E2C13937F0>
          └ <fastapi.applications.FastAPI object at 0x000001E2BF27DC00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001E2C1639BD0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E2C13937F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001E2C1639EA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001E2BC5B4C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001E2C187B8C0>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001E2C15B73E0>
    └ <contextlib._GeneratorContextManager object at 0x000001E2C17FE590>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1639D80>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001E2C17FF400>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001E2C17FF400>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1639D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C163A5F0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C163AB00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C163A5F0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C163AB00>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001E2BC6DC790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C163AB00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001E2C1393190>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C163AB00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001E2C1393190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001E2BC5B4C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001E2C187B480>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001E2C168B6F0>
    └ <contextlib._GeneratorContextManager object at 0x000001E2C168DF90>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1639FC0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001E2C168DD80>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001E2C168DD80>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1639FC0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C18112D0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C163A830>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2C1393010>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001E2BC5B4C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001E2C187A900>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001E2C168B3E0>
    └ <contextlib._GeneratorContextManager object at 0x000001E2C1850D00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C18111B0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001E2C18507C0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2C1393010>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001E2C18507C0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C18111B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C1810DC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E2C1393100>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2C1393010>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C1810DC0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001E2C1851030>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E2C1393100>
          └ <function wrap_app_handling_exceptions at 0x000001E2BC6268C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1810EE0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1810EE0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>>
          └ <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1810EE0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001E2BC6500D0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1810EE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001E2C12B2560>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1810EE0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001E2C18505B0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001E2C1810F70>
          └ <function wrap_app_handling_exceptions at 0x000001E2BC6268C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C18115A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1810E50>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001E2C1810F70>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001E2C18505B0>
                     └ <function get_request_handler.<locals>.app at 0x000001E2C12B2680>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001E2BC651BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C18517B0>, 'name': None, 'task_id': None}
                 │         └ <function get_task_results_paged at 0x000001E2C0C128C0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='name', mode='validation'), ModelField(field_...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\result.py", line 41, in get_task_results_paged
    page_data = await paging_data(db, result_select)
                      │           │   └ <sqlalchemy.sql.selectable.Select object at 0x000001E2C17FFC70>
                      │           └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C18517B0>
                      └ <function paging_data at 0x000001E2BF2BBEB0>

  File "F:\gitpush\fastapi_best_architecture\backend\common\pagination.py", line 122, in paging_data
    paginated_data: _CustomPage = await apaginate(db, select)
                                        │         │   └ <sqlalchemy.sql.selectable.Select object at 0x000001E2C17FFC70>
                                        │         └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C18517B0>
                                        └ <function apaginate at 0x000001E2BF2F9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 552, in apaginate
    return await run_async_flow(
                 └ <function run_async_flow at 0x000001E2BC696830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 72, in run_async_flow
    res = gen.throw(exc)
          │   └ <method 'throw' of 'generator' objects>
          └ <generator object _sqlalchemy_flow at 0x000001E2C168B0D0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 323, in _sqlalchemy_flow
    page = yield from generic_flow(
                      └ <function generic_flow at 0x000001E2BC696D40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flows.py", line 113, in generic_flow
    total = yield from total_flow()
                       └ functools.partial(<function _total_flow at 0x000001E2BF2F9000>, <sqlalchemy.sql.selectable.Select object at 0x000001E2C17FFC7...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 260, in _total_flow
    total = yield conn.scalar(count_query)
                  │    │      └ <sqlalchemy.sql.selectable.Select object at 0x000001E2C17FEB90>
                  │    └ <function AsyncSession.scalar at 0x000001E2BE261C60>
                  └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C18517B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 67, in run_async_flow
    res = await await_if_coro(res)
                │             └ <coroutine object AsyncSession.scalar at 0x000001E2C168AEA0>
                └ <function await_if_coro at 0x000001E2BC667640>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\utils.py", line 100, in await_if_coro
    return cast(R, await coro)
           │    │        └ <coroutine object AsyncSession.scalar at 0x000001E2C168AEA0>
           │    └ ~R
           └ <function cast at 0x000001E2BA4D5900>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 519, in scalar
    return await greenlet_spawn(
                 └ <function greenlet_spawn at 0x000001E2BC80A170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000001E2C139BE00 (otid=0x000001E2BC7DC5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2413, in scalar
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000001E2BDFED750>
           └ <sqlalchemy.orm.session.Session object at 0x000001E2C1851A20>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000001E2BDE739A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x000001E2BDCB51B0>
             └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1851D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000001E2C17FEB90>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x000001E2BDCB5480>
           └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1851D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x000001E2BDCB5630>
          └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1851D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x000001E2BDCB56C0>
           └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1851D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x000001E2BDCB5900>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1851D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'fba.task_result' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'fba.task_result' doesn't exist"), <traceback objec...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000001E2BDD65480>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000001E2BE295E40>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1851D80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ()
    │      │       └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000001E2BE326CB0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C16F3E20>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ()
           │    │      │    │              └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E2BE326DD0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C16F3E20>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C16F3E20>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E2C168AF80>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000001E2C139BE00 (otid=0x000001E2BC7DC5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E2C168AF80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ()
                   │    │               └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C16F3E20>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000001E2BE2F5BE0>
    └ <module 'asyncmy.errors' from 'F:\\gitpush\\fastapi_best_architecture\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_a...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000001E2BE2F5BE0>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:56:54.497 | ERROR    | 90d2077d3a284ab2acc4018a02a82837 | 请求异常: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-16 16:56:54.501 | ERROR    | 90d2077d3a284ab2acc4018a02a82837 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 328
               │     └ 3
               └ <function _main at 0x000001E2BA3755A0>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 328
           │    └ <function BaseProcess._bootstrap at 0x000001E2BA2D1900>
           └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E2BA2D0F70>
    └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E2BA36A2F0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>
    │    └ <function subprocess_started at 0x000001E2BABB1510>
    └ <SpawnProcess name='SpawnProcess-1' parent=21952 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=396, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E2BA36A410>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=396, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E2BAB8DD80>
           │       │   └ <uvicorn.server.Server object at 0x000001E2BA36A410>
           │       └ <function run at 0x000001E2BA376680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001E2BAC95AF0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001E2BA506050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E2BA505FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E2BA507AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E2BA46F490>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E2BAB70940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E2C1...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001E2BF27DC00>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E2BAB70940>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E2C1...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E2C1...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E2C13937F0>
          └ <fastapi.applications.FastAPI object at 0x000001E2BF27DC00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001E2C1810CA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E2C13937F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001E2C1811E10>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001E2BC5B4C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001E2C1BFC640>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001E2C15B7450>
    └ <contextlib._GeneratorContextManager object at 0x000001E2C17FF340>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1811750>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001E2C168DB70>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001E2C168DB70>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1811750>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C18116C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C18117E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001E2C1393220>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C18116C0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C18117E0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001E2BC6DC790>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C18117E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001E2C1393190>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E2C13931C0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C18117E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001E2C1393190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001E2BC5B4C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001E2C1BFC9C0>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001E2C1688B30>
    └ <contextlib._GeneratorContextManager object at 0x000001E2C1852CB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C163AA70>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001E2C1853A30>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001E2C1853A30>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C163AA70>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C163A200>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C163A440>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2C1393010>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001E2C1393130>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001E2BC5B4C10>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001E2C1C08100>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.ta...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001E2C16880B0>
    └ <contextlib._GeneratorContextManager object at 0x000001E2C1853D60>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1BE52D0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001E2C1853BB0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2C1393010>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001E2C1853BB0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001E2C1BE52D0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C1BE5630>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E2C1393100>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001E2C1393010>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001E2C1BE5630>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001E2C1C00100>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E2C1393100>
          └ <function wrap_app_handling_exceptions at 0x000001E2BC6268C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1BE5750>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1BE5750>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>>
          └ <fastapi.routing.APIRouter object at 0x000001E2BF27DD50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1BE5750>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001E2BC6500D0>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1BE5750>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001E2C12B2560>
          └ APIRoute(path='/api/v1/task/results', name='get_task_results_paged', methods=['GET'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1BE5750>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001E2C1C003A0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001E2C1BE57E0>
          └ <function wrap_app_handling_exceptions at 0x000001E2BC6268C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E2C1BE5870>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001E2C1BE56C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001E2C1BE57E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001E2C1C003A0>
                     └ <function get_request_handler.<locals>.app at 0x000001E2C12B2680>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001E2BC651BD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C1C007C0>, 'name': None, 'task_id': None}
                 │         └ <function get_task_results_paged at 0x000001E2C0C128C0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='name', mode='validation'), ModelField(field_...

  File "F:\gitpush\fastapi_best_architecture\backend\app\task\api\v1\result.py", line 41, in get_task_results_paged
    page_data = await paging_data(db, result_select)
                      │           │   └ <sqlalchemy.sql.selectable.Select object at 0x000001E2C1C00CD0>
                      │           └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C1C007C0>
                      └ <function paging_data at 0x000001E2BF2BBEB0>

  File "F:\gitpush\fastapi_best_architecture\backend\common\pagination.py", line 122, in paging_data
    paginated_data: _CustomPage = await apaginate(db, select)
                                        │         │   └ <sqlalchemy.sql.selectable.Select object at 0x000001E2C1C00CD0>
                                        │         └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C1C007C0>
                                        └ <function apaginate at 0x000001E2BF2F9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 552, in apaginate
    return await run_async_flow(
                 └ <function run_async_flow at 0x000001E2BC696830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 72, in run_async_flow
    res = gen.throw(exc)
          │   └ <method 'throw' of 'generator' objects>
          └ <generator object _sqlalchemy_flow at 0x000001E2C1BF0DD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 323, in _sqlalchemy_flow
    page = yield from generic_flow(
                      └ <function generic_flow at 0x000001E2BC696D40>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flows.py", line 113, in generic_flow
    total = yield from total_flow()
                       └ functools.partial(<function _total_flow at 0x000001E2BF2F9000>, <sqlalchemy.sql.selectable.Select object at 0x000001E2C1C00CD...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\ext\sqlalchemy.py", line 260, in _total_flow
    total = yield conn.scalar(count_query)
                  │    │      └ <sqlalchemy.sql.selectable.Select object at 0x000001E2C1C00EE0>
                  │    └ <function AsyncSession.scalar at 0x000001E2BE261C60>
                  └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E2C1C007C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\flow.py", line 67, in run_async_flow
    res = await await_if_coro(res)
                │             └ <coroutine object AsyncSession.scalar at 0x000001E2C1BF0F90>
                └ <function await_if_coro at 0x000001E2BC667640>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi_pagination\utils.py", line 100, in await_if_coro
    return cast(R, await coro)
           │    │        └ <coroutine object AsyncSession.scalar at 0x000001E2C1BF0F90>
           │    └ ~R
           └ <function cast at 0x000001E2BA4D5900>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 519, in scalar
    return await greenlet_spawn(
                 └ <function greenlet_spawn at 0x000001E2BC80A170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000001E2C1665C40 (otid=0x000001E2BC7DC5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2413, in scalar
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000001E2BDFED750>
           └ <sqlalchemy.orm.session.Session object at 0x000001E2C1C00B80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000001E2BDE739A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x000001E2BDCB51B0>
             └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1C010F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000001E2C1C00EE0>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x000001E2BDCB5480>
           └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1C010F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x000001E2BDCB5630>
          └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1C010F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x000001E2BDCB56C0>
           └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1C010F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x000001E2BDCB5900>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1C010F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'fba.task_result' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'fba.task_result' doesn't exist"), <traceback objec...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'fba.task_result\' doesn\'t exist")')

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000001E2BDD65480>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000001E2BE295E40>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E2C1C010F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ()
    │      │       └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000001E2BE326CB0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C13C7C90>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ()
           │    │      │    │              └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E2BE326DD0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C13C7C90>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C13C7C90>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E2C1BF10E0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000001E2C1665C40 (otid=0x000001E2BC7DC5D0) dead>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E2C1BF10E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ()
                   │    │               └ 'SELECT count(*) AS count_1 \nFROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E2C13C7C90>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000001E2BE2F5BE0>
    └ <module 'asyncmy.errors' from 'F:\\gitpush\\fastapi_best_architecture\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_a...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000001E2BE2F5BE0>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'fba.task_result' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT task_result.id AS id, task_result.task_id AS task_id, task_result.status AS status, task_result.result AS result, task_result.date_done AS date_done, task_result.traceback AS traceback, task_result.name AS name, task_result.args AS args, task_result.kwargs AS kwargs, task_result.worker AS worker, task_result.retries AS retries, task_result.queue AS queue 
FROM task_result) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
